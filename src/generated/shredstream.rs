// This file is @generated by prost-build.
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeEntriesRequest {
    #[prost(map = "string, message", tag = "1")]
    pub accounts: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterAccounts,
    >,
    #[prost(map = "string, message", tag = "3")]
    pub transactions: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterTransactions,
    >,
    #[prost(map = "string, message", tag = "2")]
    pub slots: ::std::collections::HashMap<
        ::prost::alloc::string::String,
        SubscribeRequestFilterSlots,
    >,
    #[prost(enumeration = "CommitmentLevel", optional, tag = "6")]
    pub commitment: ::core::option::Option<i32>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccounts {
    #[prost(string, repeated, tag = "2")]
    pub account: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "3")]
    pub owner: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(message, repeated, tag = "4")]
    pub filters: ::prost::alloc::vec::Vec<SubscribeRequestFilterAccountsFilter>,
    #[prost(bool, optional, tag = "5")]
    pub nonempty_txn_signature: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilter {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter::Filter",
        tags = "1, 2, 3, 4"
    )]
    pub filter: ::core::option::Option<subscribe_request_filter_accounts_filter::Filter>,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilter`.
pub mod subscribe_request_filter_accounts_filter {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Filter {
        #[prost(message, tag = "1")]
        Memcmp(super::SubscribeRequestFilterAccountsFilterMemcmp),
        #[prost(uint64, tag = "2")]
        Datasize(u64),
        #[prost(bool, tag = "3")]
        TokenAccountState(bool),
        #[prost(message, tag = "4")]
        Lamports(super::SubscribeRequestFilterAccountsFilterLamports),
    }
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterMemcmp {
    #[prost(uint64, tag = "1")]
    pub offset: u64,
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_memcmp::Data",
        tags = "2, 3, 4"
    )]
    pub data: ::core::option::Option<
        subscribe_request_filter_accounts_filter_memcmp::Data,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterMemcmp`.
pub mod subscribe_request_filter_accounts_filter_memcmp {
    #[derive(Clone, PartialEq, ::prost::Oneof)]
    pub enum Data {
        #[prost(bytes, tag = "2")]
        Bytes(::prost::alloc::vec::Vec<u8>),
        #[prost(string, tag = "3")]
        Base58(::prost::alloc::string::String),
        #[prost(string, tag = "4")]
        Base64(::prost::alloc::string::String),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterAccountsFilterLamports {
    #[prost(
        oneof = "subscribe_request_filter_accounts_filter_lamports::Cmp",
        tags = "1, 2, 3, 4"
    )]
    pub cmp: ::core::option::Option<
        subscribe_request_filter_accounts_filter_lamports::Cmp,
    >,
}
/// Nested message and enum types in `SubscribeRequestFilterAccountsFilterLamports`.
pub mod subscribe_request_filter_accounts_filter_lamports {
    #[derive(Clone, Copy, PartialEq, ::prost::Oneof)]
    pub enum Cmp {
        #[prost(uint64, tag = "1")]
        Eq(u64),
        #[prost(uint64, tag = "2")]
        Ne(u64),
        #[prost(uint64, tag = "3")]
        Lt(u64),
        #[prost(uint64, tag = "4")]
        Gt(u64),
    }
}
#[derive(Clone, Copy, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterSlots {
    #[prost(bool, optional, tag = "1")]
    pub filter_by_commitment: ::core::option::Option<bool>,
    #[prost(bool, optional, tag = "2")]
    pub interslot_updates: ::core::option::Option<bool>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct SubscribeRequestFilterTransactions {
    #[prost(string, repeated, tag = "3")]
    pub account_include: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "4")]
    pub account_exclude: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
    #[prost(string, repeated, tag = "6")]
    pub account_required: ::prost::alloc::vec::Vec<::prost::alloc::string::String>,
}
#[derive(Clone, PartialEq, ::prost::Message)]
pub struct Entry {
    #[prost(uint64, tag = "1")]
    pub slot: u64,
    #[prost(bytes = "vec", tag = "2")]
    pub entries: ::prost::alloc::vec::Vec<u8>,
}
#[derive(Clone, Copy, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum CommitmentLevel {
    Processed = 0,
    Confirmed = 1,
    Finalized = 2,
}
impl CommitmentLevel {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::Processed => "PROCESSED",
            Self::Confirmed => "CONFIRMED",
            Self::Finalized => "FINALIZED",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "PROCESSED" => Some(Self::Processed),
            "CONFIRMED" => Some(Self::Confirmed),
            "FINALIZED" => Some(Self::Finalized),
            _ => None,
        }
    }
}
/// Generated client implementations.
pub mod shredstream_proxy_client {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    use tonic::codegen::http::Uri;
    #[derive(Debug, Clone)]
    pub struct ShredstreamProxyClient<T> {
        inner: tonic::client::Grpc<T>,
    }
    impl ShredstreamProxyClient<tonic::transport::Channel> {
        /// Attempt to create a new client by connecting to a given endpoint.
        pub async fn connect<D>(dst: D) -> Result<Self, tonic::transport::Error>
        where
            D: TryInto<tonic::transport::Endpoint>,
            D::Error: Into<StdError>,
        {
            let conn = tonic::transport::Endpoint::new(dst)?.connect().await?;
            Ok(Self::new(conn))
        }
    }
    impl<T> ShredstreamProxyClient<T>
    where
        T: tonic::client::GrpcService<tonic::body::Body>,
        T::Error: Into<StdError>,
        T::ResponseBody: Body<Data = Bytes> + std::marker::Send + 'static,
        <T::ResponseBody as Body>::Error: Into<StdError> + std::marker::Send,
    {
        pub fn new(inner: T) -> Self {
            let inner = tonic::client::Grpc::new(inner);
            Self { inner }
        }
        pub fn with_origin(inner: T, origin: Uri) -> Self {
            let inner = tonic::client::Grpc::with_origin(inner, origin);
            Self { inner }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> ShredstreamProxyClient<InterceptedService<T, F>>
        where
            F: tonic::service::Interceptor,
            T::ResponseBody: Default,
            T: tonic::codegen::Service<
                http::Request<tonic::body::Body>,
                Response = http::Response<
                    <T as tonic::client::GrpcService<tonic::body::Body>>::ResponseBody,
                >,
            >,
            <T as tonic::codegen::Service<
                http::Request<tonic::body::Body>,
            >>::Error: Into<StdError> + std::marker::Send + std::marker::Sync,
        {
            ShredstreamProxyClient::new(InterceptedService::new(inner, interceptor))
        }
        /// Compress requests with the given encoding.
        ///
        /// This requires the server to support it otherwise it might respond with an
        /// error.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.send_compressed(encoding);
            self
        }
        /// Enable decompressing responses.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.inner = self.inner.accept_compressed(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_decoding_message_size(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.inner = self.inner.max_encoding_message_size(limit);
            self
        }
        pub async fn subscribe_entries(
            &mut self,
            request: impl tonic::IntoRequest<super::SubscribeEntriesRequest>,
        ) -> std::result::Result<
            tonic::Response<tonic::codec::Streaming<super::Entry>>,
            tonic::Status,
        > {
            self.inner
                .ready()
                .await
                .map_err(|e| {
                    tonic::Status::unknown(
                        format!("Service was not ready: {}", e.into()),
                    )
                })?;
            let codec = tonic::codec::ProstCodec::default();
            let path = http::uri::PathAndQuery::from_static(
                "/shredstream.ShredstreamProxy/SubscribeEntries",
            );
            let mut req = request.into_request();
            req.extensions_mut()
                .insert(
                    GrpcMethod::new("shredstream.ShredstreamProxy", "SubscribeEntries"),
                );
            self.inner.server_streaming(req, path, codec).await
        }
    }
}
/// Generated server implementations.
pub mod shredstream_proxy_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ShredstreamProxyServer.
    #[async_trait]
    pub trait ShredstreamProxy: std::marker::Send + std::marker::Sync + 'static {
        /// Server streaming response type for the SubscribeEntries method.
        type SubscribeEntriesStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::Entry, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        async fn subscribe_entries(
            &self,
            request: tonic::Request<super::SubscribeEntriesRequest>,
        ) -> std::result::Result<
            tonic::Response<Self::SubscribeEntriesStream>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct ShredstreamProxyServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ShredstreamProxyServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ShredstreamProxyServer<T>
    where
        T: ShredstreamProxy,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::Body>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/shredstream.ShredstreamProxy/SubscribeEntries" => {
                    #[allow(non_camel_case_types)]
                    struct SubscribeEntriesSvc<T: ShredstreamProxy>(pub Arc<T>);
                    impl<
                        T: ShredstreamProxy,
                    > tonic::server::ServerStreamingService<
                        super::SubscribeEntriesRequest,
                    > for SubscribeEntriesSvc<T> {
                        type Response = super::Entry;
                        type ResponseStream = T::SubscribeEntriesStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<super::SubscribeEntriesRequest>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ShredstreamProxy>::subscribe_entries(&inner, request)
                                    .await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = SubscribeEntriesSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.server_streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(
                            tonic::body::Body::default(),
                        );
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ShredstreamProxyServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "shredstream.ShredstreamProxy";
    impl<T> tonic::server::NamedService for ShredstreamProxyServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
